<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;9ac92bf6-5eda-4b22-8fdc-************&quot;,&quot;conversations&quot;:{&quot;d0e26153-d942-45fb-8225-4ffc190da2d3&quot;:{&quot;id&quot;:&quot;d0e26153-d942-45fb-8225-4ffc190da2d3&quot;,&quot;createdAtIso&quot;:&quot;2025-06-04T11:29:53.260Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-04T11:29:53.260Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;d6627cff-d098-4ab1-8488-2bea3f721985&quot;},&quot;9ac92bf6-5eda-4b22-8fdc-************&quot;:{&quot;id&quot;:&quot;9ac92bf6-5eda-4b22-8fdc-************&quot;,&quot;createdAtIso&quot;:&quot;2025-06-04T11:29:53.358Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-04T11:29:53.358Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;33e84dd2-68c0-48ae-b5b1-a358345596d4&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;}" />
      </map>
    </option>
  </component>
</project>